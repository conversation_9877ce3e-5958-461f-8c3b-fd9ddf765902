"use server";

import {
  pauseSubscription as razorpayPauseSubscription,
  resumeSubscription as razorpayResumeSubscription,
  getSubscription as razorpayGetSubscription
} from "@/lib/razorpay/services/subscription";
import { PricingPlan, pricingPlans } from "@/lib/PricingPlans";
import { ActionResponse } from "./types";
import {
  getUserAndProfile,
  revalidateSubscriptionPaths,
  createErrorResponse,
  createSuccessResponse
} from "./utils";
import {
  SUBSCRIPTION_STATUS
} from "@/lib/razorpay/webhooks/handlers/subscription-constants";

/**
 * Pause a subscription
 * @returns The pause result
 */
export async function pauseUserSubscription(): Promise<ActionResponse> {
  try {
    // Get user and profile with subscription information
    const { user, profile, error } = await getUserAndProfile("has_active_subscription");

    if (error) {
      return createErrorResponse(error);
    }

    // If profile is null or user doesn't have an active subscription, return error
    if (!profile || !profile.has_active_subscription) {
      return createErrorResponse("User does not have an active subscription");
    }

    // Get the subscription ID from the payment_subscriptions table using centralized logic
    const supabase = await (await import("@/utils/supabase/server")).createClient();
    const { data: subscriptions, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("razorpay_subscription_id")
      .eq("business_profile_id", user.id)
      .eq("subscription_status", SUBSCRIPTION_STATUS.ACTIVE)
      .limit(1);

    if (subscriptionError || !subscriptions || subscriptions.length === 0) {
      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return createErrorResponse("Could not find an active subscription");
    }

    const subscriptionId = subscriptions[0].razorpay_subscription_id;

    if (!subscriptionId) {
      return createErrorResponse("No active subscription found");
    }

    // Pause the subscription in Razorpay
    const result = await razorpayPauseSubscription(subscriptionId, "now");

    if (!result.success) {
      console.error("[RAZORPAY_ERROR] Error pausing subscription:", result.error);
      return createErrorResponse("Failed to pause subscription");
    }

    // IMPORTANT: Don't update the database here - let the webhook handle it
    // When Razorpay pauses the subscription, it will send a subscription.halted webhook
    // which will properly handle the database updates and preserve original plan
    console.log(`[RAZORPAY_PAUSE] Successfully paused subscription ${subscriptionId} in Razorpay. Webhook will handle database updates.`);

    // Revalidate paths
    revalidateSubscriptionPaths();

    return createSuccessResponse({
      message: "Subscription paused successfully",
      subscription: result.data
    });
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error in pauseUserSubscription:", error);
    return createErrorResponse(error instanceof Error ? error.message : "An unknown error occurred");
  }
}

/**
 * Activate a paused subscription
 * @returns The activation result
 */
export async function activateUserSubscription(): Promise<ActionResponse> {
  try {
    // Get user and profile
    const { user, profile, error } = await getUserAndProfile("has_active_subscription");

    if (error) {
      return createErrorResponse(error);
    }

    // If profile is null, return error
    if (!profile) {
      return createErrorResponse("User profile not found");
    }

    // Get the subscription ID from the payment_subscriptions table
    const supabase = await (await import("@/utils/supabase/server")).createClient();
    const { data: subscriptions, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("razorpay_subscription_id, subscription_paused_at")
      .eq("business_profile_id", user.id)
      .eq("subscription_status", SUBSCRIPTION_STATUS.HALTED)
      .limit(1);

    if (subscriptionError || !subscriptions || subscriptions.length === 0) {
      console.error("[RAZORPAY_ERROR] Error fetching paused subscription:", subscriptionError);
      return createErrorResponse("Could not find a paused subscription");
    }

    const subscriptionId = subscriptions[0].razorpay_subscription_id;

    if (!subscriptionId) {
      return createErrorResponse("No paused subscription found");
    }

    // Resume the subscription in Razorpay
    const result = await razorpayResumeSubscription(subscriptionId);

    if (!result.success) {
      console.error("[RAZORPAY_ERROR] Error resuming subscription:", result.error);
      return createErrorResponse("Failed to resume subscription");
    }

    // IMPORTANT: Don't update the database here - let the webhook handle it
    // When Razorpay resumes the subscription, it will send a subscription.activated webhook
    // which will properly restore the original plan and clear the pause data
    console.log(`[RAZORPAY_RESUME] Successfully resumed subscription ${subscriptionId} in Razorpay. Webhook will handle database updates.`);

    // Revalidate paths
    revalidateSubscriptionPaths();

    // Ensure we're returning a properly structured response
    const response = await createSuccessResponse({
      message: "Subscription resumed successfully",
      subscription: result.data
    });

    console.log("[RAZORPAY_DEBUG] Returning response from activateUserSubscription:", response);
    return response;
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error in activateUserSubscription:", error);
    const errorResponse = await createErrorResponse(error instanceof Error ? error.message : "An unknown error occurred");
    console.log("[RAZORPAY_DEBUG] Returning error response from activateUserSubscription:", errorResponse);
    return errorResponse;
  }
}

/**
 * Get subscription details
 * @returns The subscription details
 */
export async function getSubscriptionDetails(): Promise<ActionResponse> {
  try {
    // Get user and profile
    // Note: trial_end_date is in business_profiles table, not payment_subscriptions
    // Our updated getUserAndProfile function will handle this correctly
    const { user, profile, error } = await getUserAndProfile(`
      has_active_subscription,
      subscription_start_date,
      trial_end_date,
      cancellation_requested_at,
      subscription_paused_at
    `);

    if (error) {
      return createErrorResponse(error);
    }

    // Check if profile exists
    if (!profile) {
      return createErrorResponse("User profile not found");
    }

    // Get plan details
    let currentPlan: PricingPlan | undefined;

    // Get the subscription from the payment_subscriptions table
    const supabase = await (await import("@/utils/supabase/server")).createClient();
    const { data: subscriptions, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("business_profile_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1);

    if (subscriptionError) {
      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
    }

    // Check Razorpay subscription status if available
    let subscriptionStatus = null;
    let currentPlanId: string | null = null;
    let planCycle: string | null = null;

    if (subscriptions && subscriptions.length > 0) {
      // Get plan details from subscription
      currentPlanId = subscriptions[0].plan_id;
      planCycle = subscriptions[0].plan_cycle;

      // Get plan details
      if (currentPlanId && planCycle) {
        const allPlans = pricingPlans(planCycle as "monthly" | "yearly");
        currentPlan = allPlans.find((plan) => plan.id === currentPlanId);
      }

      // Get Razorpay subscription status if available
      if (subscriptions[0].razorpay_subscription_id) {
        const subscriptionId = subscriptions[0].razorpay_subscription_id;
        const statusResult = await razorpayGetSubscription(subscriptionId);

        if (statusResult.success) {
          subscriptionStatus = statusResult.data;
        }
      }
    }

    // Refunds are only available for payment issues or subscription problems
    // Always set isWithinRefundWindow to false as the 7-day refund policy has been removed
    const isWithinRefundWindow = false;

    return createSuccessResponse({
      ...profile,
      subscription: subscriptions && subscriptions.length > 0 ? subscriptions[0] : null,
      currentPlan,
      subscriptionStatus,
      isWithinRefundWindow,
    });
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error in getSubscriptionDetails:", error);
    return createErrorResponse(error instanceof Error ? error.message : "An unknown error occurred");
  }
}
