import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { cancelSubscription } from "@/lib/razorpay/services/subscription";

/**
 * POST /api/subscriptions/:id/cancel
 *
 * Cancels a subscription in Razorpay and updates the database record.
 *
 * Important: This endpoint only marks the subscription as cancelled in our database
 * by setting the subscription_status and cancellation_requested_at fields.
 * The actual deletion of the record happens when <PERSON><PERSON><PERSON><PERSON> confirms the cancellation
 * via webhook (subscription.cancelled event).
 *
 * Request body:
 * ```json
 * {
 *   "cancelAtCycleEnd": false,
 *   "reason": "Optional cancellation reason"
 * }
 * ```
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "id": "sub_00000000000001",
 *     "entity": "subscription",
 *     "plan_id": "plan_00000000000001",
 *     "customer_id": "cust_D00000000000001",
 *     "status": "cancelled",
 *     "current_start": 1580453311,
 *     "current_end": 1581013800,
 *     "ended_at": 1580288092,
 *     "quantity": 1,
 *     "notes": {
 *       "notes_key_1": "Tea, <PERSON>, Hot",
 *       "notes_key_2": "Tea, Earl Grey… decaf."
 *     },
 *     "charge_at": 1580453311,
 *     "start_at": 1577385991,
 *     "end_at": 1603737000,
 *     "auth_attempts": 0,
 *     "total_count": 6,
 *     "paid_count": 1,
 *     "customer_notify": true,
 *     "created_at": 1580283117,
 *     "expire_by": 1581013800,
 *     "short_url": "https://rzp.io/i/z3b1R61A9",
 *     "has_scheduled_changes": false,
 *     "change_scheduled_at": null,
 *     "source": "api",
 *     "offer_id": "offer_JHD834hjbxzhd38d",
 *     "remaining_count": 5,
 *   }
 * }
 * ```
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    // Parse request body
    const body = await request.json();
    const { cancelAtCycleEnd = false, reason = null } = body;

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the user has access to this subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (subscriptionError) {
      // If no subscription is found with this ID, return a 404
      if (subscriptionError.code === "PGRST116") {
        return NextResponse.json(
          { success: false, error: "Subscription not found" },
          { status: 404 }
        );
      }

      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription" },
        { status: 500 }
      );
    }

    // Check if the user is authorized to cancel this subscription
    const isOwner = subscription.business_profile_id === user.id;

    if (!isOwner) {
      return NextResponse.json(
        { success: false, error: "Unauthorized to cancel this subscription" },
        { status: 403 }
      );
    }

    // Check if the subscription is already cancelled
    if (subscription.subscription_status === "cancelled") {
      return NextResponse.json(
        { success: false, error: "Subscription is already cancelled" },
        { status: 400 }
      );
    }

    // Get admin client for database operations
    const adminClient = createAdminClient();
    const now = new Date().toISOString();

    // Check if this is an authenticated subscription
    if (subscription.subscription_status === "authenticated") {
      console.log("[RAZORPAY_DEBUG] Handling cancellation for authenticated subscription in API route using pause endpoint");

      // For authenticated subscriptions, we'll use the pause endpoint which will cancel it
      // According to Razorpay docs: "If you pause a Subscription in the authenticated state, it goes to the cancelled state"
      const { pauseSubscription } = await import("@/lib/razorpay/services/subscription");
      const pauseResult = await pauseSubscription(subscriptionId, "now", true);

      if (!pauseResult.success) {
        console.error("[RAZORPAY_ERROR] Error pausing authenticated subscription:", pauseResult.error);

        // Even if the API call fails, we'll still update our database
        console.log("[RAZORPAY_DEBUG] Proceeding with database update despite API failure");
      } else {
        console.log("[RAZORPAY_DEBUG] Successfully paused/cancelled authenticated subscription");
      }

      // IMPORTANT: Don't update the database here - let the webhook handle it
      // When Razorpay cancels the subscription, it will send a subscription.cancelled webhook
      // which will properly handle the database updates and downgrade to free plan
      console.log(`[RAZORPAY_CANCEL] Successfully cancelled authenticated subscription ${subscriptionId} in Razorpay. Webhook will handle database updates.`);

      // Don't update the business profile here
      // Let the webhook handler handle all the updates when it receives the official event from Razorpay

      // Use the pause result data if available, otherwise create a basic response
      const responseData = pauseResult.success ? pauseResult.data : { id: subscriptionId, status: "cancelled" };

      return NextResponse.json(
        {
          success: true,
          data: {
            ...responseData,
            db_subscription: {
              id: subscription.id,
              cancellation_requested: true,
              cancellation_requested_at: now,
              cancellation_reason: reason
            }
          }
        },
        { status: 200 }
      );
    }

    // For active subscriptions, cancel in Razorpay
    const result = await cancelSubscription(subscriptionId, cancelAtCycleEnd);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // adminClient is already created above

    // IMPORTANT: Don't update the database here - let the webhook handle it
    // When Razorpay cancels the subscription, it will send a subscription.cancelled webhook
    // which will properly handle the database updates and downgrade to free plan
    console.log(`[RAZORPAY_CANCEL] Successfully cancelled subscription ${subscriptionId} in Razorpay. Webhook will handle database updates.`);

    // Don't update the business profile here
    // Let the webhook handler handle all the updates when it receives the official event from Razorpay

    // Return the cancelled subscription with additional database information
    return NextResponse.json(
      {
        success: true,
        data: {
          ...result.data,
          db_subscription: {
            id: subscription.id,
            cancellation_requested: true,
            cancellation_requested_at: now,
            cancellation_reason: reason
          }
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error cancelling subscription:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
