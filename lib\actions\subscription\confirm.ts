"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { ActionResponse } from "./types";
import { revalidateSubscriptionPaths, createErrorResponse, createSuccessResponse } from "./utils";

/**
 * Confirm a subscription payment
 * @param subscriptionId The subscription ID
 * @param paymentId The payment ID
 * @returns The confirmation result
 */
export async function confirmSubscriptionPayment(
  subscriptionId: string,
  paymentId: string
): Promise<ActionResponse> {
  try {
    console.log(`[SUBSCRIPTION_CONFIRM] Processing subscription ${subscriptionId}`);

    // Get the user from the session
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error("[SUBSCRIPTION_CONFIRM] Authentication error:", authError);
      return createErrorResponse("Authentication required");
    }

    // Get the user's business profile
    const { data: profile, error: profileError } = await supabase
      .from("business_profiles")
      .select("id, has_active_subscription")
      .eq("id", user.id)
      .single();

    if (profileError || !profile) {
      console.error("[SUBSCRIPTION_CONFIRM] Profile error:", profileError);
      return createErrorResponse("Could not fetch business profile");
    }

    // Check if there's an existing subscription in the payment_subscriptions table
    const { data: existingSubscriptionData } = await supabase
      .from("payment_subscriptions")
      .select("razorpay_subscription_id, subscription_status")
      .eq("business_profile_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    // Check if this subscription already exists in our database
    const { data: newSubscriptionCheck } = await supabase
      .from("payment_subscriptions")
      .select("id")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    // If this is a new subscription ID and we have an existing subscription,
    // we need to determine if this is a legitimate plan switch or an error
    if (existingSubscriptionData?.razorpay_subscription_id &&
        existingSubscriptionData.razorpay_subscription_id !== subscriptionId &&
        !newSubscriptionCheck) {



      // Get subscription details from Razorpay to check if it's a plan switch
      const { getSubscription } = await import('@/lib/razorpay/services/subscription');
      const subscriptionDetails = await getSubscription(subscriptionId);

      // Check if the existing subscription is in an expired or cancelled state
      // If so, we should allow the new subscription as the user is trying to resubscribe
      const isExistingSubscriptionInactive =
        existingSubscriptionData.subscription_status === "expired" ||
        existingSubscriptionData.subscription_status === "cancelled";

      if (isExistingSubscriptionInactive) {

        // Continue with the subscription confirmation
      } else {
        // For trial users or new subscriptions, we'll allow this even without the plan switch flag
        // This is a more permissive approach that handles various scenarios
        let allowNewSubscription = false;

        if (subscriptionDetails.success) {
          // Check if this is explicitly marked as a plan switch
          if (subscriptionDetails.data?.notes &&
              subscriptionDetails.data.notes.is_plan_switch === "true") {

            allowNewSubscription = true;
          }
          // Check if this is a future payment (trial user)
          else if (subscriptionDetails.data?.start_at) {
            const startAt = subscriptionDetails.data.start_at;
            const now = Math.floor(Date.now() / 1000);

            if (startAt > now) {

              allowNewSubscription = true;
            }
          }
          // Check if this is a new subscription for the same user
          else if (subscriptionDetails.data?.customer_id) {
            // Get the customer ID from the existing subscription
            const { data: existingSubscription } = await getSubscription(existingSubscriptionData.razorpay_subscription_id);

            if (existingSubscription &&
                existingSubscription.customer_id === subscriptionDetails.data.customer_id) {

              allowNewSubscription = true;
            }
          }
        }

        // If we've determined this is a legitimate new subscription, allow it
        if (allowNewSubscription) {
          console.log(`[SUBSCRIPTION_CONFIRM] Allowing new subscription ${subscriptionId} for user with existing subscription ${existingSubscriptionData.razorpay_subscription_id}`);
        } else {
          // Check if this is a CREATE_AND_CANCEL flow by looking at the subscription notes
          const { getSubscription } = await import('@/lib/razorpay/services/subscription');
          const newSubscriptionDetails = await getSubscription(subscriptionId);

          if (newSubscriptionDetails.success && newSubscriptionDetails.data?.notes?.old_subscription_id === existingSubscriptionData.razorpay_subscription_id) {
            console.log(`[SUBSCRIPTION_CONFIRM] This is a CREATE_AND_CANCEL flow - new subscription ${subscriptionId} replaces old subscription ${existingSubscriptionData.razorpay_subscription_id}`);
            allowNewSubscription = true;
          } else {
            // This appears to be an unauthorized new subscription, reject it
            console.error(`[SUBSCRIPTION_CONFIRM] Subscription ID mismatch: ${existingSubscriptionData.razorpay_subscription_id} != ${subscriptionId}`);
            return createErrorResponse("Subscription ID mismatch");
          }
        }
      }
    }

    // Get the subscription from the database
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    // Get admin client for database operations
    const adminSupabase = createAdminClient();

    if (subscriptionError) {
      console.error("[SUBSCRIPTION_CONFIRM] Subscription error:", subscriptionError);
      return createErrorResponse("Could not fetch subscription details");
    }

    // If subscription record doesn't exist, create one
    if (!subscription) {
      console.log(`[SUBSCRIPTION_CONFIRM] No subscription record found for ${subscriptionId}, creating one`);

      // Get subscription details from Razorpay
      const { getSubscription } = await import('@/lib/razorpay/services/subscription');
      const subscriptionDetails = await getSubscription(subscriptionId);

      if (!subscriptionDetails.success || !subscriptionDetails.data) {
        console.error(`[SUBSCRIPTION_CONFIRM] Failed to get subscription details from Razorpay for ${subscriptionId}`);
        return createErrorResponse("Failed to get subscription details from Razorpay");
      }

      // Extract plan details from notes or plan_id
      let planType = subscriptionDetails.data.notes?.plan_type;
      let planCycle = subscriptionDetails.data.notes?.plan_cycle;

      // If plan type and cycle are not in notes, try to determine from plan_id
      if (!planType || !planCycle) {


        // Use centralized plan configuration to map Razorpay plan ID to plan details
        const { getPlanByRazorpayPlanId } = await import('@/lib/config/plans');
        const planDetails = getPlanByRazorpayPlanId(subscriptionDetails.data.plan_id);

        if (planDetails) {
          planType = planDetails.id;
          // Determine cycle by checking which Razorpay plan ID matches
          planCycle = planDetails.razorpayPlanIds.monthly === subscriptionDetails.data.plan_id ? "monthly" : "yearly";

        } else {
          // Default to basic monthly if we can't determine
          planType = "basic";
          planCycle = "monthly";

        }
      }

      // Check if this is a trial user with a future payment date
      let subscriptionStatus = "active"; // Default to active

      try {
        const startAt = subscriptionDetails.data.start_at;
        const now = Math.floor(Date.now() / 1000); // Current time in seconds

        if (startAt && startAt > now) {
          subscriptionStatus = "authenticated"; // Set to authenticated for future payments

        }
      } catch (error) {
        // If we can't determine future payment, just continue with the default status
        console.error("[SUBSCRIPTION_CONFIRM] Error checking future payment:", error);
      }

      // First, check if there's an existing subscription for this business
      const { data: existingBusinessSubscription, error: existingBusinessError } = await adminSupabase
        .from("payment_subscriptions")
        .select("id, razorpay_subscription_id")
        .eq("business_profile_id", user.id)
        .maybeSingle();

      if (existingBusinessError) {
        console.error("[SUBSCRIPTION_CONFIRM] Error checking for existing business subscription:", existingBusinessError);
        return createErrorResponse("Could not check for existing subscription");
      }

      // Prepare the subscription data
      const subscriptionData = {
        razorpay_subscription_id: subscriptionId,
        razorpay_customer_id: subscriptionDetails.data.customer_id || null,
        subscription_status: subscriptionStatus, // Set based on whether it's a future payment
        plan_id: planType,
        plan_cycle: planCycle,
        subscription_start_date: subscriptionDetails.data.current_start ? new Date(subscriptionDetails.data.current_start * 1000).toISOString() : null,
        subscription_expiry_time: subscriptionDetails.data.current_end ? new Date(subscriptionDetails.data.current_end * 1000).toISOString() : null,
        subscription_charge_time: subscriptionDetails.data.charge_at ? new Date(subscriptionDetails.data.charge_at * 1000).toISOString() : null,
        updated_at: new Date().toISOString(),
        last_payment_id: paymentId,
        last_payment_date: new Date().toISOString()
      };

      // If there's an existing subscription for this business, update it
      if (existingBusinessSubscription) {


        // Get the current status of the existing subscription
        const { data: existingSubDetails, error: existingSubError } = await adminSupabase
          .from("payment_subscriptions")
          .select("subscription_status")
          .eq("id", existingBusinessSubscription.id)
          .single();

        if (existingSubError) {
          console.error("[SUBSCRIPTION_CONFIRM] Error fetching existing subscription details:", existingSubError);
          // Continue anyway - we'll assume it's active and try to cancel it
        }

        const isExistingSubscriptionInactive =
          existingSubDetails?.subscription_status === "expired" ||
          existingSubDetails?.subscription_status === "cancelled";

        // If the existing subscription has a different Razorpay ID and is not already expired/cancelled, cancel it
        if (existingBusinessSubscription.razorpay_subscription_id !== subscriptionId &&
            !isExistingSubscriptionInactive &&
            existingBusinessSubscription.razorpay_subscription_id) {


          // Check the status of the existing subscription to determine cancellation method
          const existingStatus = existingSubDetails?.subscription_status;
          let cancelResult;

          if (existingStatus === "authenticated") {
            // For authenticated subscriptions, use pause which will cancel them
            // According to Razorpay docs: "If you pause a Subscription in the authenticated state, it goes to the cancelled state"
            const { pauseSubscription } = await import('@/lib/razorpay/services/subscription');
            cancelResult = await pauseSubscription(existingBusinessSubscription.razorpay_subscription_id, "now", true);
          } else {
            // For active subscriptions, use direct cancellation
            const { cancelSubscription } = await import('@/lib/razorpay/services/subscription');
            cancelResult = await cancelSubscription(existingBusinessSubscription.razorpay_subscription_id, false);
          }

          if (!cancelResult.success) {
            console.error(`[SUBSCRIPTION_CONFIRM] Error cancelling old subscription ${existingBusinessSubscription.razorpay_subscription_id}:`, cancelResult.error);
            // Continue anyway - we still want to update with the new subscription
          } else {
            console.log(`[SUBSCRIPTION_CONFIRM] Successfully cancelled old subscription ${existingBusinessSubscription.razorpay_subscription_id} (status: ${existingStatus})`);
          }
        } else if (existingBusinessSubscription.razorpay_subscription_id === null) {


        } else if (isExistingSubscriptionInactive) {

        }

        // Update the existing subscription
        const { error: updateError } = await adminSupabase
          .from("payment_subscriptions")
          .update(subscriptionData)
          .eq("id", existingBusinessSubscription.id);

        if (updateError) {
          console.error("[SUBSCRIPTION_CONFIRM] Error updating existing subscription:", updateError);
          return createErrorResponse("Could not update existing subscription");
        }


      } else {
        // If no existing subscription for this business, create a new one


        // Add created_at for new records
        const newSubscriptionData = {
          ...subscriptionData,
          business_profile_id: user.id,
          created_at: new Date().toISOString()
        };

        // Create a new subscription record
        const { error: insertError } = await adminSupabase
          .from("payment_subscriptions")
          .insert(newSubscriptionData);

        if (insertError) {
          console.error("[SUBSCRIPTION_CONFIRM] Error creating subscription record:", insertError);

          // If it's a unique constraint violation, try updating instead
          if (insertError.code === '23505') {
            console.log(`[SUBSCRIPTION_CONFIRM] Unique constraint violation, trying to update instead`);

            // Get the existing subscription again (in case it was created in a race condition)
            const { data: raceSubscription, error: raceError } = await adminSupabase
              .from("payment_subscriptions")
              .select("id")
              .eq("business_profile_id", user.id)
              .single();

            if (raceError) {
              console.error("[SUBSCRIPTION_CONFIRM] Error fetching subscription in race condition:", raceError);
              return createErrorResponse("Could not create or update subscription record");
            }

            // Update the existing subscription
            const { error: raceUpdateError } = await adminSupabase
              .from("payment_subscriptions")
              .update(subscriptionData)
              .eq("id", raceSubscription.id);

            if (raceUpdateError) {
              console.error("[SUBSCRIPTION_CONFIRM] Error updating subscription in race condition:", raceUpdateError);
              return createErrorResponse("Could not update subscription record in race condition");
            }


          } else {
            return createErrorResponse("Could not create subscription record");
          }
        } else {

        }
      }
    } else {
      // Check if this is a trial user with a future payment date
      let subscriptionStatus = "active"; // Default to active

      try {
        // Get subscription details from Razorpay to check if this is a future payment
        const { getSubscription } = await import('@/lib/razorpay/services/subscription');
        const razorpaySubscription = await getSubscription(subscriptionId);

        if (razorpaySubscription.success && razorpaySubscription.data) {
          const startAt = razorpaySubscription.data.start_at;
          const now = Math.floor(Date.now() / 1000); // Current time in seconds

          if (startAt && startAt > now) {
            subscriptionStatus = "authenticated"; // Set to authenticated for future payments
  
          }
        }
      } catch (error) {
        // If we can't determine future payment, just continue with the default status
        console.error("[SUBSCRIPTION_CONFIRM] Error checking future payment for update:", error);
      }

      // Get subscription details from Razorpay
      const { getSubscription } = await import('@/lib/razorpay/services/subscription');
      const razorpaySubscription = await getSubscription(subscriptionId);

      // Define the type for update data
      interface SubscriptionUpdateData {
        last_payment_id: string;
        last_payment_date: string;
        subscription_status: string;
        updated_at: string;
        razorpay_customer_id?: string | null;
        subscription_start_date?: string | null;
        subscription_expiry_time?: string | null;
        subscription_charge_time?: string | null;
      }

      // Prepare update data
      const updateData: SubscriptionUpdateData = {
        last_payment_id: paymentId,
        last_payment_date: new Date().toISOString(),
        subscription_status: subscriptionStatus, // Set based on whether it's a future payment
        updated_at: new Date().toISOString()
      };

      // Add Razorpay subscription details if available
      if (razorpaySubscription.success && razorpaySubscription.data) {
        const subData = razorpaySubscription.data;
        updateData.razorpay_customer_id = subData.customer_id || null;
        updateData.subscription_start_date = subData.current_start ? new Date(subData.current_start * 1000).toISOString() : null;
        updateData.subscription_expiry_time = subData.current_end ? new Date(subData.current_end * 1000).toISOString() : null;
        updateData.subscription_charge_time = subData.charge_at ? new Date(subData.charge_at * 1000).toISOString() : null;
      }

      // IMPORTANT: Don't update the database here - let the webhook handle it
      // Payment confirmations should trigger webhooks that handle database updates
      console.log(`[SUBSCRIPTION_CONFIRM] Payment confirmed for subscription ${subscription.razorpay_subscription_id}. Webhook will handle database updates.`);
    }

    // IMPORTANT: Don't update the database here - let the webhook handle it
    // Payment confirmations should trigger webhooks that handle database updates
    console.log(`[SUBSCRIPTION_CONFIRM] Payment confirmed for subscription ${subscriptionId}. Webhook will handle database updates.`);



    // Revalidate paths
    revalidateSubscriptionPaths();

    // Check if this is a trial user with a future payment date
    let isFuturePayment = false;
    let message = "Subscription payment confirmed successfully";

    // Get subscription details from Razorpay to check if this is a future payment
    try {
      const { getSubscription } = await import('@/lib/razorpay/services/subscription');
      const razorpaySubscription = await getSubscription(subscriptionId);

      if (razorpaySubscription.success && razorpaySubscription.data) {
        const startAt = razorpaySubscription.data.start_at;
        const now = Math.floor(Date.now() / 1000); // Current time in seconds

        if (startAt && startAt > now) {
          isFuturePayment = true;
          message = "Subscription authorized successfully. Payment will be processed when your trial ends.";

        }
      }
    } catch (subscriptionError) {
      // If we can't get the subscription details, just continue with the default message
      console.error("[SUBSCRIPTION_CONFIRM] Error checking future payment:", subscriptionError);
    }

    return createSuccessResponse({
      message,
      subscription_id: subscriptionId,
      payment_id: paymentId,
      is_future_payment: isFuturePayment
    });
  } catch (error) {
    console.error("[SUBSCRIPTION_CONFIRM] Unexpected error:", error);
    return createErrorResponse(
      error instanceof Error ? error.message : "An unexpected error occurred"
    );
  }
}
