import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { pauseSubscription } from "@/lib/razorpay/services/subscription";

/**
 * POST /api/subscriptions/:id/pause
 *
 * Pauses a subscription in Razorpay and updates the database record
 *
 * Request body:
 * ```json
 * {
 *   "pauseAt": "now"
 * }
 * ```
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "id": "sub_00000000000001",
 *     "entity": "subscription",
 *     "plan_id": "plan_00000000000001",
 *     "status": "paused",
 *     "current_start": null,
 *     "current_end": null,
 *     "ended_at": null,
 *     "quantity": 1,
 *     "notes": {
 *       "notes_key_1": "Tea, <PERSON>, Hot",
 *       "notes_key_2": "<PERSON>, <PERSON>… decaf."
 *     },
 *     "charge_at": null,
 *     "start_at": 1580626111,
 *     "end_at": 1583433000,
 *     "auth_attempts": 0,
 *     "total_count": 6,
 *     "paid_count": 0,
 *     "customer_notify": true,
 *     "created_at": 1580280581,
 *     "paused_at": 1590280581,
 *     "expire_by": 1580626111,
 *     "pause_initiated_by": "self",
 *     "short_url": "https://rzp.io/i/z3b1R61A9",
 *     "has_scheduled_changes": false,
 *     "change_scheduled_at": null,
 *     "source": "api",
 *     "offer_id": "offer_JHD834hjbxzhd38d",
 *     "remaining_count": 6,
 *     "db_subscription": {
 *       "id": "123e4567-e89b-12d3-a456-426614174000",
 *       "subscription_status": "halted",
 *       "subscription_paused_at": "2023-01-01T00:00:00Z"
 *     }
 *   }
 * }
 * ```
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    // Parse request body
    const body = await request.json();
    const { pauseAt = "now" } = body;

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the user has access to this subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (subscriptionError) {
      // If no subscription is found with this ID, return a 404
      if (subscriptionError.code === "PGRST116") {
        return NextResponse.json(
          { success: false, error: "Subscription not found" },
          { status: 404 }
        );
      }

      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription" },
        { status: 500 }
      );
    }

    // Check if the user is authorized to pause this subscription
    const isOwner = subscription.business_profile_id === user.id;

    if (!isOwner) {
      return NextResponse.json(
        { success: false, error: "Unauthorized to pause this subscription" },
        { status: 403 }
      );
    }

    // Check if the subscription is already paused
    if (subscription.subscription_status === "halted") {
      return NextResponse.json(
        { success: false, error: "Subscription is already paused" },
        { status: 400 }
      );
    }

    // Pause the subscription in Razorpay
    const result = await pauseSubscription(subscriptionId, pauseAt as "now" | "cycle_end");

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Get admin client to update the subscription record
    const adminClient = createAdminClient();

    // IMPORTANT: Don't update the database here - let the webhook handle it
    // When Razorpay pauses the subscription, it will send a subscription.halted webhook
    // which will properly handle the database updates and preserve original plan
    console.log(`[RAZORPAY_PAUSE] Successfully paused subscription ${subscriptionId} in Razorpay. Webhook will handle database updates.`);

    // Return the paused subscription data from Razorpay
    return NextResponse.json(
      {
        success: true,
        data: {
          ...result.data,
          message: "Subscription paused successfully. Database will be updated via webhook."
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error pausing subscription:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
