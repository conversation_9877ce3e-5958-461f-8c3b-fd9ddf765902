"use server";

import { cancelSubscription as razorpayCancelSubscription } from "@/lib/razorpay/services/subscription";
import { ActionResponse } from "../types";
import {
  getUserAndProfile,
  revalidateSubscriptionPaths,
  createErrorResponse,
  createSuccessResponse,
  doesSubscriptionBelongToUser
} from "../utils";

/**
 * Schedule subscription change (cancellation at end of cycle)
 * @param subscriptionId The subscription ID
 * @returns The scheduling result
 */
export async function scheduleSubscriptionChange(
  subscriptionId: string
): Promise<ActionResponse> {
  // Get user and profile
  const { user, error } = await getUserAndProfile();

  if (error || !user) {
    return createErrorResponse(error || "User not found");
  }

  // Verify that the subscription belongs to the user
  const belongsToUser = await doesSubscriptionBelongToUser(user.id, subscriptionId);
  if (!belongsToUser) {
    return createErrorResponse("Subscription does not belong to user");
  }

  // Get subscription from payment_subscriptions table using admin client
  const adminClient = await import("@/utils/supabase/admin").then(mod => mod.createAdminClient());
  const { data: subscription, error: subscriptionError } = await adminClient
    .from("payment_subscriptions")
    .select("razorpay_subscription_id")
    .eq("business_profile_id", user?.id || "")
    .eq("subscription_status", "active")
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError || !subscription?.razorpay_subscription_id) {
    console.error("Error fetching subscription:", subscriptionError);
    return createErrorResponse("Could not find active subscription");
  }

  // Schedule cancellation at end of cycle (cancel_at_cycle_end = 1)
  const result = await razorpayCancelSubscription(
    subscription.razorpay_subscription_id,
    true // true means cancel at cycle end
  );

  if (!result.success) {
    console.error("Error scheduling cancellation:", result.error);
    return createErrorResponse("Could not schedule cancellation");
  }

  // IMPORTANT: Don't update the database here - let the webhook handle it
  // When Razorpay schedules the cancellation, it will send webhooks that handle database updates
  console.log(`[RAZORPAY_SCHEDULE] Successfully scheduled cancellation for subscription ${subscription.razorpay_subscription_id} in Razorpay. Webhook will handle database updates.`);

  // Revalidate paths
  revalidateSubscriptionPaths();

  return createSuccessResponse({
    message:
      "Your subscription is scheduled to cancel at the end of the billing cycle. You can subscribe to a new plan after the current one ends.",
  });
}
