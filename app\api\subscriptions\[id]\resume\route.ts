import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { resumeSubscription } from "@/lib/razorpay/services/subscription";

/**
 * POST /api/subscriptions/:id/resume
 *
 * Resumes a paused subscription in Razorpay and updates the database record
 *
 * Example request:
 * ```json
 * {
 *   "resume_at": "now"
 * }
 * ```
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "id": "sub_00000000000001",
 *     "entity": "subscription",
 *     "plan_id": "plan_00000000000001",
 *     "status": "active",
 *     "current_start": null,
 *     "current_end": null,
 *     "ended_at": null,
 *     "quantity": 1,
 *     "notes": {
 *       "notes_key_1": "<PERSON>, <PERSON>, Hot",
 *       "notes_key_2": "<PERSON>, <PERSON>… decaf."
 *     },
 *     "charge_at": 1580453311,
 *     "start_at": 1580626111,
 *     "end_at": 1583433000,
 *     "auth_attempts": 0,
 *     "total_count": 6,
 *     "paid_count": 0,
 *     "customer_notify": true,
 *     "created_at": 1580280581,
 *     "paused_at": 1590280581,
 *     "expire_by": 1580626111,
 *     "pause_initiated_by": null,
 *     "short_url": "https://rzp.io/i/z3b1R61A9",
 *     "has_scheduled_changes": false,
 *     "change_scheduled_at": null,
 *     "source": "api",
 *     "offer_id": "offer_JHD834hjbxzhd38d",
 *     "remaining_count": 6,
 *     "db_subscription": {
 *       "id": "123e4567-e89b-12d3-a456-426614174000",
 *       "subscription_status": "active",
 *       "subscription_paused_at": null
 *     }
 *   }
 * }
 * ```
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the user has access to this subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (subscriptionError) {
      // If no subscription is found with this ID, return a 404
      if (subscriptionError.code === "PGRST116") {
        return NextResponse.json(
          { success: false, error: "Subscription not found" },
          { status: 404 }
        );
      }

      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription" },
        { status: 500 }
      );
    }

    // Check if the user is authorized to resume this subscription
    const isOwner = subscription.business_profile_id === user.id;

    if (!isOwner) {
      return NextResponse.json(
        { success: false, error: "Unauthorized to resume this subscription" },
        { status: 403 }
      );
    }

    // Check if the subscription is paused
    if (subscription.subscription_status !== "halted") {
      return NextResponse.json(
        { success: false, error: "Subscription is not paused" },
        { status: 400 }
      );
    }

    // Resume the subscription in Razorpay
    const result = await resumeSubscription(subscriptionId);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Get admin client to update the subscription record
    const adminClient = createAdminClient();

    // Check if this subscription has original plan data to restore
    const { data: subscriptionData, error: fetchError } = await adminClient
      .from("payment_subscriptions")
      .select("original_plan_id, original_plan_cycle, plan_id, plan_cycle")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (fetchError) {
      console.error("[RAZORPAY_ERROR] Error fetching subscription data for resume:", fetchError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription data" },
        { status: 500 }
      );
    }

    // Prepare the update data for resume
    const now = new Date().toISOString();
    const updateData: Record<string, any> = {
      subscription_status: "active",
      subscription_paused_at: null,
      updated_at: now
    };

    // If we have original plan data, restore it
    if (subscriptionData.original_plan_id && subscriptionData.original_plan_cycle) {
      console.log(`[RAZORPAY_RESUME] Restoring original plan: ${subscriptionData.original_plan_id}/${subscriptionData.original_plan_cycle}`);
      updateData.plan_id = subscriptionData.original_plan_id;
      updateData.plan_cycle = subscriptionData.original_plan_cycle;
      updateData.original_plan_id = null; // Clear after restoration
      updateData.original_plan_cycle = null; // Clear after restoration
    } else {
      console.log(`[RAZORPAY_RESUME] No original plan data found, keeping current plan: ${subscriptionData.plan_id}/${subscriptionData.plan_cycle}`);
    }

    // ENHANCED: Use atomic RPC function for transaction safety
    // Note: We don't automatically set status back to "online" as the user needs to
    // explicitly set their card back to online through the dashboard
    const { data: atomicResult, error: atomicError } = await adminClient.rpc('update_subscription_atomic', {
      p_subscription_id: subscriptionId,
      p_new_status: 'active',
      p_business_profile_id: subscription.business_profile_id,
      p_has_active_subscription: true,
      p_additional_data: updateData,
      p_webhook_timestamp: null
    });

    if (atomicError || !atomicResult?.success) {
      console.error("[RAZORPAY_ERROR] Error updating subscription atomically:", atomicError || atomicResult?.error);
      // Continue anyway, as the Razorpay subscription was resumed successfully
    } else {
      console.log(`[RAZORPAY_DEBUG] Successfully updated subscription ${subscriptionId} and business profile ${subscription.business_profile_id} atomically`);
      console.log(`[RAZORPAY_DEBUG] Note: Business profile status remains offline until user explicitly sets it back to online`);
    }

    // Return the resumed subscription with additional database information
    return NextResponse.json(
      {
        success: true,
        data: {
          ...result.data,
          db_subscription: {
            id: subscription.id,
            subscription_status: "active",
            subscription_paused_at: null
          }
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error resuming subscription:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
