"use server";

import { PlanCycle, PlanId, ActionResponse } from "../types";
import {
  getUserAndProfile,
  revalidateSubscriptionPaths,
  createErrorResponse,
  createSuccessResponse
} from "../utils";
import { getSubscriptionRazorpayPlanId } from "@/lib/config/plans";
// Removed prorated refund calculation
import { PaymentMethodType } from "@/lib/types/subscription";

/**
 * Switch from an authenticated subscription to a new plan
 * @param currentSubscriptionId The current authenticated subscription ID
 * @param newPlanId The new plan ID
 * @param newPlanCycle The new plan cycle
 * @returns The subscription result
 */
export async function switchAuthenticatedSubscription(
  currentSubscriptionId: string,
  newPlanId: PlanId,
  newPlanCycle: PlanCycle
): Promise<ActionResponse> {
  // Get user and profile
  const { user, profile, error } = await getUserAndProfile("has_active_subscription, trial_end_date");

  if (error) {
    return createErrorResponse(error);
  }

  // Ensure user exists
  if (!user) {
    return createErrorResponse("User not found");
  }

  // Get subscription from payment_subscriptions table
  const supabase = await import("@/utils/supabase/admin").then(mod => mod.createAdminClient());
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select(`
      razorpay_subscription_id,
      subscription_status,
      plan_id,
      plan_cycle,
      subscription_start_date,
      subscription_expiry_time,
      last_payment_id
    `)
    .eq("business_profile_id", user.id)
    .eq("razorpay_subscription_id", currentSubscriptionId)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription:", subscriptionError);
    return createErrorResponse("Error fetching subscription details");
  }

  // Verify that the subscription belongs to the user and is in authenticated state
  if (!subscription?.razorpay_subscription_id) {
    return createErrorResponse("Subscription does not belong to user");
  }

  if (subscription.subscription_status !== "authenticated") {
    return createErrorResponse("Subscription is not in authenticated state");
  }

  // Check if the user is trying to subscribe to the same plan they already have
  if (subscription.plan_id === newPlanId && subscription.plan_cycle === newPlanCycle) {
    return createErrorResponse(
      "You are already subscribed to this plan. Please choose a different plan or cycle."
    );
  }

  // We will NOT cancel the current subscription yet
  // Instead, we'll create a new subscription and let the webhook handler
  // cancel the old one after the new one is authenticated

  console.log("[SUBSCRIPTION_SWITCH] Creating new subscription without cancelling the old one yet");
  console.log("[SUBSCRIPTION_SWITCH] Old subscription ID will be stored in notes for webhook handler to process");

  // Step 2: Create a new subscription for the desired plan
  console.log("[SUBSCRIPTION_SWITCH] Creating new subscription with plan:", newPlanId, newPlanCycle);

  // Check if the user is in trial period
  const trialEndDate = profile?.trial_end_date ? new Date(profile.trial_end_date) : null;
  const isInTrial = trialEndDate && trialEndDate > new Date();

  // Get the Razorpay plan ID based on the plan type and cycle
  let razorpayPlanId: string;
  try {
    razorpayPlanId = getSubscriptionRazorpayPlanId(newPlanId, newPlanCycle);
  } catch (error) {
    return createErrorResponse(error instanceof Error ? error.message : "Invalid plan selected");
  }

  // Get the existing subscription details to extract customer ID
  const { getSubscription } = await import("@/lib/razorpay/services/subscription");
  const existingSubscriptionDetails = await getSubscription(currentSubscriptionId);

  // Extract customer ID from existing subscription if available
  let customerId = null;
  if (existingSubscriptionDetails.success && existingSubscriptionDetails.data?.customer_id) {
    customerId = existingSubscriptionDetails.data.customer_id;
    console.log(`[SUBSCRIPTION_SWITCH] Found customer ID from existing subscription: ${customerId}`);
  } else {
    console.log(`[SUBSCRIPTION_SWITCH] No customer ID found in existing subscription, checking profile`);

    // If no customer ID in existing subscription, try to find or create one
    const { findCustomerByEmail, createCustomer } = await import("@/lib/razorpay/services/customer");

    // Get customer email from profile
    const { data: profile } = await supabase
      .from("business_profiles")
      .select("business_name, contact_email, phone")
      .eq("id", user.id)
      .single();

    const customerEmail = profile?.contact_email || user.email || '';

    if (customerEmail) {
      // Check if customer already exists in Razorpay
      console.log(`[SUBSCRIPTION_SWITCH] Checking if customer with email ${customerEmail} already exists`);
      const existingCustomer = await findCustomerByEmail(customerEmail);

      if (existingCustomer.success && existingCustomer.data) {
        // Customer exists, use their ID
        customerId = existingCustomer.data.id;
        console.log(`[SUBSCRIPTION_SWITCH] Found existing customer with ID: ${customerId}`);
      } else {
        // Customer doesn't exist, create a new one
        console.log(`[SUBSCRIPTION_SWITCH] No existing customer found, creating new customer`);
        const newCustomer = await createCustomer({
          name: profile?.business_name || (user.user_metadata && typeof user.user_metadata === 'object' ? (user.user_metadata as Record<string, string>).full_name : undefined) || 'Customer',
          email: customerEmail,
          contact: profile?.phone || '',
          notes: {
            user_id: user.id,
            business_name: profile?.business_name || ''
          }
        });

        if (newCustomer.success && newCustomer.data) {
          customerId = newCustomer.data.id;
          console.log(`[SUBSCRIPTION_SWITCH] Created new customer with ID: ${customerId}`);
        } else {
          console.error('[SUBSCRIPTION_SWITCH] Failed to create customer:', newCustomer.error);
          // Continue without customer ID
        }
      }
    }
  }

  // Get user profile for additional information
  const { data: profileData } = await supabase
    .from("business_profiles")
    .select("business_name, contact_email, phone")
    .eq("id", user.id)
    .single();

  // Prepare subscription parameters
  const { createSubscription: createRazorpaySubscription } = await import("@/lib/razorpay/services/subscription");
  const subscriptionParams = {
    plan_id: razorpayPlanId,
    total_count: newPlanCycle === "monthly" ? 120 : 10, // 10 years (120 months for monthly, 10 years for yearly)
    quantity: 1, // Default to 1
    customer_notify: true,
    notes: {
      business_profile_id: user.id, // Same as user_id for our app
      old_subscription_id: currentSubscriptionId, // Store the old subscription ID for the webhook handler
      plan_type: newPlanId,
      plan_cycle: newPlanCycle,
      is_plan_switch: "true", // Flag to indicate this is a plan switch
      user_id: user.id, // Add user ID to notes for easier identification
      business_name: profileData?.business_name || '',
      email: profileData?.contact_email || user.email || '',
      phone: profileData?.phone || '',
      prorated_refund_amount: '',
      original_plan_id: '',
      original_plan_cycle: '',
      original_payment_id: ''
    },
    start_at: undefined as number | undefined,
    ...(customerId && { customer_id: customerId }) // Add customer ID if available
  };

  // If user is on trial, set start_at to trial end date
  if (isInTrial && trialEndDate) {
    // Convert trial end date to Unix timestamp (seconds)
    const startAt = Math.floor(trialEndDate.getTime() / 1000);
    subscriptionParams.start_at = startAt;
    console.log(`[SUBSCRIPTION_SWITCH] User is on trial until ${trialEndDate.toISOString()}, setting start_at to ${startAt}`);
  } else {
    console.log(`[SUBSCRIPTION_SWITCH] No trial or trial has ended, immediate payment required`);
  }

  // Prorated refunds have been removed
  // Removed unused variable

  // Create a new subscription with the new plan
  const createResult = await createRazorpaySubscription(subscriptionParams);

  if (!createResult.success || !createResult.data?.id) {
    console.error("[SUBSCRIPTION_SWITCH] Error creating new subscription:", createResult.error);
    return createErrorResponse("Could not create new subscription");
  }

  // Revalidate paths
  revalidateSubscriptionPaths();

  // Return the subscription details for payment authorization
  return createSuccessResponse({
    message: "New subscription created. Please complete the payment authorization.",
    id: createResult.data.id, // Use 'id' instead of 'subscription_id' to match the expected format
    short_url: createResult.data.short_url,
    requires_authorization: true
  });
}

/**
 * Switch from an active subscription to a new plan
 * @param currentSubscriptionId The current active subscription ID
 * @param newPlanId The new plan ID
 * @param newPlanCycle The new plan cycle
 * @returns The subscription result
 */
export async function switchActiveSubscription(
  currentSubscriptionId: string,
  newPlanId: PlanId,
  newPlanCycle: PlanCycle
): Promise<ActionResponse> {
  // Get user and profile
  const { user, error } = await getUserAndProfile("has_active_subscription, trial_end_date");

  if (error) {
    return createErrorResponse(error);
  }

  // Ensure user exists
  if (!user) {
    return createErrorResponse("User not found");
  }

  // Get subscription from payment_subscriptions table
  const supabase = await import("@/utils/supabase/admin").then(mod => mod.createAdminClient());
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select(`
      razorpay_subscription_id,
      subscription_status,
      plan_id,
      plan_cycle,
      subscription_start_date,
      subscription_expiry_time,
      last_payment_id,
      last_payment_method
    `)
    .eq("business_profile_id", user.id)
    .eq("razorpay_subscription_id", currentSubscriptionId)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription:", subscriptionError);
    return createErrorResponse("Error fetching subscription details");
  }

  // Verify that the subscription belongs to the user and is in active state
  if (!subscription?.razorpay_subscription_id) {
    return createErrorResponse("Subscription does not belong to user");
  }

  if (subscription.subscription_status !== "active") {
    return createErrorResponse("Subscription is not in active state");
  }

  // Check if the user is trying to subscribe to the same plan they already have
  if (subscription.plan_id === newPlanId && subscription.plan_cycle === newPlanCycle) {
    return createErrorResponse(
      "You are already subscribed to this plan. Please choose a different plan or cycle."
    );
  }

  // Get the Razorpay plan ID based on the plan type and cycle
  let razorpayPlanId: string;
  try {
    razorpayPlanId = getSubscriptionRazorpayPlanId(newPlanId, newPlanCycle);
  } catch (error) {
    return createErrorResponse(error instanceof Error ? error.message : "Invalid plan selected");
  }

  // Check if the payment method is UPI or emandate
  const paymentMethod = subscription.last_payment_method?.toLowerCase() || '';
  const isUpiOrEmandate = paymentMethod === 'upi' || paymentMethod === 'emandate' || paymentMethod === 'enach';

  console.log(`[SUBSCRIPTION_SWITCH] Payment method: ${paymentMethod}, isUpiOrEmandate: ${isUpiOrEmandate}`);

  // If payment method is UPI or emandate, we need to create a new subscription instead of updating
  if (isUpiOrEmandate) {
    console.log(`[SUBSCRIPTION_SWITCH] Payment method is ${paymentMethod}, creating new subscription instead of updating`);

    // Use the special function for UPI/emandate payment methods
    return switchActiveSubscriptionWithNewPayment(
      currentSubscriptionId,
      newPlanId,
      newPlanCycle,
      paymentMethod as PaymentMethodType
    );
  }

  // For other payment methods, we'll use the Razorpay update API
  const { updateSubscription, getSubscription } = await import("@/lib/razorpay/services/subscription");

  console.log(`[SUBSCRIPTION_SWITCH] Updating active subscription ${currentSubscriptionId} to plan ${newPlanId} (${newPlanCycle})`);

  // Check if we're changing to a different period (monthly <-> yearly)
  const isDifferentPeriod = subscription.plan_cycle !== newPlanCycle;
  const updateParams: {
    plan_id: string;
    schedule_change_at: string;
    customer_notify: boolean;
    remaining_count?: number;
  } = {
    plan_id: razorpayPlanId,
    schedule_change_at: "now",
    customer_notify: true
  };

  // If changing to a different period, we need to include remaining_count
  if (isDifferentPeriod) {
    console.log(`[SUBSCRIPTION_SWITCH] Different period detected (${subscription.plan_cycle} -> ${newPlanCycle}), fetching current subscription details`);

    // Get current subscription details from Razorpay to get remaining_count
    const currentSubscriptionDetails = await getSubscription(currentSubscriptionId);

    if (currentSubscriptionDetails.success && currentSubscriptionDetails.data) {
      const currentRemainingCount = currentSubscriptionDetails.data.remaining_count;

      // Calculate appropriate remaining_count for the target period
      let targetRemainingCount: number;

      if (subscription.plan_cycle === "monthly" && newPlanCycle === "yearly") {
        // Converting from monthly to yearly
        // Monthly plans have max 120 cycles, yearly plans have max 10 cycles
        // Convert remaining months to remaining years (rounded up to ensure coverage)
        targetRemainingCount = Math.min(Math.ceil(currentRemainingCount / 12), 10);
      } else if (subscription.plan_cycle === "yearly" && newPlanCycle === "monthly") {
        // Converting from yearly to monthly
        // Convert remaining years to remaining months
        targetRemainingCount = Math.min(currentRemainingCount * 12, 120);
      } else {
        // Same period (shouldn't happen due to isDifferentPeriod check, but safety fallback)
        targetRemainingCount = currentRemainingCount;
      }

      updateParams.remaining_count = targetRemainingCount;
      console.log(`[SUBSCRIPTION_SWITCH] Converting remaining_count from ${currentRemainingCount} (${subscription.plan_cycle}) to ${targetRemainingCount} (${newPlanCycle})`);
    } else {
      console.error(`[SUBSCRIPTION_SWITCH] Failed to fetch current subscription details:`, currentSubscriptionDetails.error);
      return createErrorResponse("Failed to fetch current subscription details for period change");
    }
  }

  // Update the subscription with the new plan
  const result = await updateSubscription(currentSubscriptionId, updateParams);

  if (!result.success) {
    console.error("[SUBSCRIPTION_SWITCH] Error updating subscription:", result.error);

    // Check if the error is related to payment method
    const errorStr = typeof result.error === 'string'
      ? result.error
      : JSON.stringify(result.error);

    if (errorStr.includes('payment mode is upi') ||
        errorStr.includes('payment mode is emandate') ||
        errorStr.includes('payment mode is enach')) {
      console.log(`[SUBSCRIPTION_SWITCH] Error indicates payment method issue, falling back to new subscription creation`);

      // If the error is related to payment method, fall back to creating a new subscription
      return switchActiveSubscriptionWithNewPayment(
        currentSubscriptionId,
        newPlanId,
        newPlanCycle,
        paymentMethod as PaymentMethodType
      );
    }

    return createErrorResponse(
      typeof result.error === 'string'
        ? result.error
        : result.error
          ? JSON.stringify(result.error)
          : "Failed to update subscription"
    );
  }

  // Import edge-safe subscription logic to determine has_active_subscription
  const { EdgeSubscriptionStateManager } = await import('@/lib/subscription/edge-validation');
  const hasActiveSubscription = EdgeSubscriptionStateManager.shouldHaveActiveSubscription(
    subscription.subscription_status,
    newPlanId
  );

  // IMPORTANT: Don't update the database here - let the webhook handle it
  // When Razorpay switches the subscription, it will send webhooks that handle database updates
  console.log(`[RAZORPAY_SWITCH] Successfully switched subscription ${currentSubscriptionId} in Razorpay. Webhook will handle database updates.`);

  // Revalidate paths
  revalidateSubscriptionPaths();

  // Check if this is a plan downgrade
  const isPlanDowngrade =
    (subscription.plan_id === 'pro' && (newPlanId === 'growth' || newPlanId === 'basic' || newPlanId === 'free')) ||
    (subscription.plan_id === 'growth' && (newPlanId === 'basic' || newPlanId === 'free')) ||
    (subscription.plan_id === 'basic' && newPlanId === 'free');

  // If this is a plan downgrade, log it
  if (isPlanDowngrade) {
    console.log(`[PLAN_CHANGE] Plan downgrade detected from ${subscription.plan_id} to ${newPlanId}`);
    console.log(`[PLAN_CHANGE] Product availability will be automatically handled by database trigger`);


  }

  return createSuccessResponse({
    message: "Your subscription plan has been updated successfully."
  });
}

/**
 * Switch from an active subscription to a new plan when payment method is UPI or emandate
 * This creates a new subscription instead of updating the existing one, as Razorpay
 * doesn't allow updating subscriptions with UPI or emandate payment methods.
 *
 * @param currentSubscriptionId The current active subscription ID
 * @param newPlanId The new plan ID
 * @param newPlanCycle The new plan cycle
 * @param paymentMethod The payment method used for the subscription
 * @returns The subscription result
 */
export async function switchActiveSubscriptionWithNewPayment(
  currentSubscriptionId: string,
  newPlanId: PlanId,
  newPlanCycle: PlanCycle,
  paymentMethod: PaymentMethodType
): Promise<ActionResponse> {
  // Get user and profile
  const { user, error } = await getUserAndProfile("has_active_subscription, trial_end_date");

  if (error) {
    return createErrorResponse(error);
  }

  // Ensure user exists
  if (!user) {
    return createErrorResponse("User not found");
  }

  // Get subscription from payment_subscriptions table
  const supabase = await import("@/utils/supabase/admin").then(mod => mod.createAdminClient());
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select(`
      razorpay_subscription_id,
      subscription_status,
      plan_id,
      plan_cycle,
      subscription_start_date,
      subscription_expiry_time,
      last_payment_id,
      last_payment_method
    `)
    .eq("business_profile_id", user.id)
    .eq("razorpay_subscription_id", currentSubscriptionId)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription:", subscriptionError);
    return createErrorResponse("Error fetching subscription details");
  }

  // Verify that the subscription belongs to the user and is in active state
  if (!subscription?.razorpay_subscription_id) {
    return createErrorResponse("Subscription does not belong to user");
  }

  if (subscription.subscription_status !== "active") {
    return createErrorResponse("Subscription is not in active state");
  }

  // Check if the user is trying to subscribe to the same plan they already have
  if (subscription.plan_id === newPlanId && subscription.plan_cycle === newPlanCycle) {
    return createErrorResponse(
      "You are already subscribed to this plan. Please choose a different plan or cycle."
    );
  }

  // Get the Razorpay plan ID based on the plan type and cycle
  let razorpayPlanId: string;
  try {
    razorpayPlanId = getSubscriptionRazorpayPlanId(newPlanId, newPlanCycle);
  } catch (error) {
    return createErrorResponse(error instanceof Error ? error.message : "Invalid plan selected");
  }

  // Get the existing subscription details to extract customer ID
  const { getSubscription } = await import("@/lib/razorpay/services/subscription");
  const existingSubscriptionDetails = await getSubscription(currentSubscriptionId);

  // Extract customer ID from existing subscription if available
  let customerId = null;
  if (existingSubscriptionDetails.success && existingSubscriptionDetails.data?.customer_id) {
    customerId = existingSubscriptionDetails.data.customer_id;
    console.log(`[SUBSCRIPTION_SWITCH] Found customer ID from existing subscription: ${customerId}`);
  } else {
    console.log(`[SUBSCRIPTION_SWITCH] No customer ID found in existing subscription, checking profile`);

    // If no customer ID in existing subscription, try to find or create one
    const { findCustomerByEmail, createCustomer } = await import("@/lib/razorpay/services/customer");

    // Get customer email from profile
    const { data: profile } = await supabase
      .from("business_profiles")
      .select("business_name, contact_email, phone")
      .eq("id", user.id)
      .single();

    const customerEmail = profile?.contact_email || user.email || '';

    if (customerEmail) {
      // Check if customer already exists in Razorpay
      console.log(`[SUBSCRIPTION_SWITCH] Checking if customer with email ${customerEmail} already exists`);
      const existingCustomer = await findCustomerByEmail(customerEmail);

      if (existingCustomer.success && existingCustomer.data) {
        // Customer exists, use their ID
        customerId = existingCustomer.data.id;
        console.log(`[SUBSCRIPTION_SWITCH] Found existing customer with ID: ${customerId}`);
      } else {
        // Customer doesn't exist, create a new one
        console.log(`[SUBSCRIPTION_SWITCH] No existing customer found, creating new customer`);
        const newCustomer = await createCustomer({
          name: profile?.business_name || (user.user_metadata && typeof user.user_metadata === 'object' ? (user.user_metadata as Record<string, string>).full_name : undefined) || 'Customer',
          email: customerEmail,
          contact: profile?.phone || '',
          notes: {
            user_id: user.id,
            business_name: profile?.business_name || ''
          }
        });

        if (newCustomer.success && newCustomer.data) {
          customerId = newCustomer.data.id;
          console.log(`[SUBSCRIPTION_SWITCH] Created new customer with ID: ${customerId}`);
        } else {
          console.error('[SUBSCRIPTION_SWITCH] Failed to create customer:', newCustomer.error);
          // Continue without customer ID
        }
      }
    }
  }

  // Get user profile for additional information
  const { data: profileData } = await supabase
    .from("business_profiles")
    .select("business_name, contact_email, phone")
    .eq("id", user.id)
    .single();

  // NOTE: We no longer cancel the current subscription immediately
  // Instead, the old subscription will be cancelled only after the new subscription becomes active
  // This is handled in the subscription.activated webhook handler to ensure continuity of service
  console.log(`[SUBSCRIPTION_SWITCH] Creating new subscription for plan switch. Old subscription ${currentSubscriptionId} will be cancelled after new subscription becomes active`);

  // Prepare subscription parameters
  const { createSubscription: createRazorpaySubscription } = await import("@/lib/razorpay/services/subscription");
  const subscriptionParams = {
    plan_id: razorpayPlanId,
    total_count: newPlanCycle === "monthly" ? 120 : 10, // 10 years (120 months for monthly, 10 years for yearly)
    quantity: 1, // Default to 1
    customer_notify: true,
    notes: {
      business_profile_id: user.id, // Same as user_id for our app
      old_subscription_id: currentSubscriptionId, // Store the old subscription ID for the webhook handler
      plan_type: newPlanId,
      plan_cycle: newPlanCycle,
      is_plan_switch: "true", // Flag to indicate this is a plan switch
      user_id: user.id, // Add user ID to notes for easier identification
      business_name: profileData?.business_name || '',
      email: profileData?.contact_email || user.email || '',
      phone: profileData?.phone || '',
      payment_method: paymentMethod, // Store the payment method for reference
      original_plan_id: subscription.plan_id,
      original_plan_cycle: subscription.plan_cycle
    },
    ...(customerId && { customer_id: customerId }) // Add customer ID if available
  };

  // Create a new subscription with the new plan
  const createResult = await createRazorpaySubscription(subscriptionParams);

  if (!createResult.success || !createResult.data?.id) {
    console.error("[SUBSCRIPTION_SWITCH] Error creating new subscription:", createResult.error);
    return createErrorResponse("Could not create new subscription");
  }

  // Revalidate paths
  revalidateSubscriptionPaths();

  // Return the subscription details for payment authorization
  return createSuccessResponse({
    message: "A new subscription has been created for your plan switch. Please complete the payment authorization to activate your new plan. Your current subscription will remain active until the new one is confirmed.",
    id: createResult.data.id,
    short_url: createResult.data.short_url,
    requires_authorization: true
  });
}