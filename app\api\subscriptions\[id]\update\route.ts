import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { updateSubscription } from "@/lib/razorpay/services/subscription";
import { getRazorpayPlanId } from "@/lib/config/plans";
import { PlanType, PlanCycle } from "@/lib/config/plans";
import {
  isActivePaidSubscription
} from "@/lib/razorpay/webhooks/handlers/subscription-state-manager";

/**
 * PATCH /api/subscriptions/:id/update
 *
 * Updates a subscription in Razorpay and the database record
 *
 * Request body:
 * ```json
 * {
 *   "planId": "basic",
 *   "planCycle": "monthly",
 *   "quantity": 1,
 *   "scheduleChangeAt": "now",
 *   "customerNotify": true,
 *   "notes": {
 *     "note_key_1": "Note value 1",
 *     "note_key_2": "Note value 2"
 *   }
 * }
 * ```
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "id": "sub_00000000000001",
 *     "entity": "subscription",
 *     "plan_id": "plan_00000000000002",
 *     "customer_id": "cust_00000000000001",
 *     "status": "active",
 *     "current_start": 1580453311,
 *     "current_end": 1581013800,
 *     "ended_at": null,
 *     "quantity": 5,
 *     "notes": {
 *       "note_key_1": "Note value 1",
 *       "note_key_2": "Note value 2"
 *     },
 *     "charge_at": 1580453311,
 *     "start_at": 1580453311,
 *     "end_at": 1606588200,
 *     "auth_attempts": 0,
 *     "total_count": 6,
 *     "paid_count": 0,
 *     "customer_notify": true,
 *     "created_at": 1580283807,
 *     "expire_by": 1580626111,
 *     "short_url": "https://rzp.io/i/yeDkUKy",
 *     "has_scheduled_changes": false,
 *     "change_scheduled_at": null,
 *     "source": "api",
 *     "offer_id": "offer_JHD834hjbxzhd38d",
 *     "remaining_count": 6,
 *     "db_subscription": {
 *       "id": "123e4567-e89b-12d3-a456-426614174000",
 *       "plan_id": "basic",
 *       "plan_cycle": "monthly",
 *       "updated_at": "2023-01-01T00:00:00Z"
 *     }
 *   }
 * }
 * ```
 */
export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string  }> }) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    // Parse request body
    const body = await request.json();
    const {
      planId,
      planCycle,
      quantity,
      scheduleChangeAt = "now",
      customerNotify = true,
      remainingCount,
      startAt,
      offerId,
      notes = {}
    } = body;

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the user has access to this subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (subscriptionError) {
      // If no subscription is found with this ID, return a 404
      if (subscriptionError.code === "PGRST116") {
        return NextResponse.json(
          { success: false, error: "Subscription not found" },
          { status: 404 }
        );
      }

      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription" },
        { status: 500 }
      );
    }

    // Check if the user is authorized to update this subscription
    const isOwner = subscription.business_profile_id === user.id;

    if (!isOwner) {
      return NextResponse.json(
        { success: false, error: "Unauthorized to update this subscription" },
        { status: 403 }
      );
    }

    // Check if the subscription is in a state that can be updated using centralized logic
    if (!isActivePaidSubscription(subscription.subscription_status)) {
      return NextResponse.json(
        { success: false, error: "Subscription cannot be updated in its current state" },
        { status: 400 }
      );
    }

    // Prepare update parameters
    const updateParams: Record<string, unknown> = {
      schedule_change_at: scheduleChangeAt,
      customer_notify: customerNotify
    };

    // Add optional parameters if provided
    if (planId && planCycle) {
      const razorpayPlanId = getRazorpayPlanId(
        planId as PlanType,
        planCycle as PlanCycle
      );

      if (!razorpayPlanId) {
        return NextResponse.json(
          { success: false, error: "Invalid plan or plan cycle" },
          { status: 400 }
        );
      }

      updateParams.plan_id = razorpayPlanId;
    }

    if (quantity !== undefined) {
      updateParams.quantity = quantity;
    }

    if (remainingCount !== undefined) {
      updateParams.remaining_count = remainingCount;
    }

    if (startAt !== undefined) {
      updateParams.start_at = startAt;
    }

    if (offerId !== undefined) {
      updateParams.offer_id = offerId;
    }

    if (Object.keys(notes).length > 0) {
      updateParams.notes = notes;
    }

    // Update the subscription in Razorpay
    const result = await updateSubscription(subscriptionId, updateParams);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Get admin client to update the subscription record
    const adminClient = createAdminClient();

    // Update the subscription record in the database
    const now = new Date().toISOString();
    const dbUpdateParams: Record<string, unknown> = {
      updated_at: now
    };

    // Update plan details if provided
    if (planId) {
      dbUpdateParams.plan_id = planId;
    }

    if (planCycle) {
      dbUpdateParams.plan_cycle = planCycle;
    }

    // IMPORTANT: Don't update the database here - let the webhook handle it
    // When Razorpay updates the subscription, it will send a subscription.updated webhook
    // which will properly handle the database updates
    console.log(`[RAZORPAY_UPDATE] Successfully updated subscription ${subscriptionId} in Razorpay. Webhook will handle database updates.`);

    // Return the updated subscription with additional database information
    return NextResponse.json(
      {
        success: true,
        data: {
          ...result.data,
          db_subscription: {
            id: subscription.id,
            plan_id: planId || subscription.plan_id,
            plan_cycle: planCycle || subscription.plan_cycle,
            updated_at: now
          }
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error updating subscription:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
