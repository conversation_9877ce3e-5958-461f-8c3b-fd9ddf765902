"use server";

import {
  updateSubscription as razorpayUpdateSubscription,
  getSubscription as razorpayGetSubscription
} from "@/lib/razorpay/services/subscription";
import { PlanCycle, PlanId, ActionResponse } from "../types";
import {
  getUserAndProfile,
  revalidateSubscriptionPaths,
  createErrorResponse,
  createSuccessResponse
} from "../utils";
import { getSubscriptionRazorpayPlanId } from "@/lib/config/plans";


/**
 * Change plan using the manage API
 * @param newPlanId The new plan ID
 * @param newPlanCycle The new plan cycle
 * @returns The plan change result
 */
export async function changePlanWithManage(
  newPlanId: PlanId,
  newPlanCycle: PlanCycle
): Promise<ActionResponse> {
  // Get user and profile
  const { user, profile, error } = await getUserAndProfile(
    "has_active_subscription"
  );

  if (error) {
    return createErrorResponse(error);
  }

  // Get subscription from payment_subscriptions table
  const supabase = await import("@/utils/supabase/admin").then(mod => mod.createAdminClient());

  // Check if user is null
  if (!user) {
    return createErrorResponse("User not found");
  }

  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("razorpay_subscription_id, subscription_status, plan_id, plan_cycle")
    .eq("business_profile_id", user.id)
    .eq("subscription_status", "active")
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription:", subscriptionError);
    return createErrorResponse("Error fetching subscription details");
  }

  // If profile is null or user doesn't have an active subscription, return error
  if (!profile || !profile.has_active_subscription || !subscription?.razorpay_subscription_id) {
    return createErrorResponse("User does not have an active subscription");
  }

  // If no change in plan or cycle, return early
  if (
    subscription?.plan_id === newPlanId &&
    subscription?.plan_cycle === newPlanCycle
  ) {
    return createSuccessResponse({ no_change: true });
  }

  // Get the Razorpay plan ID based on the plan type and cycle
  let razorpayPlanId: string;
  try {
    razorpayPlanId = getSubscriptionRazorpayPlanId(newPlanId, newPlanCycle);
  } catch (error) {
    return createErrorResponse(error instanceof Error ? error.message : "Invalid plan selected");
  }

  // Check if we're changing to a different period (monthly <-> yearly)
  const isDifferentPeriod = subscription.plan_cycle !== newPlanCycle;
  const updateParams: {
    plan_id: string;
    schedule_change_at: string;
    customer_notify: boolean;
    remaining_count?: number;
  } = {
    plan_id: razorpayPlanId,
    schedule_change_at: "now",
    customer_notify: true
  };

  // If changing to a different period, we need to include remaining_count
  if (isDifferentPeriod) {
    console.log(`[SUBSCRIPTION_CHANGE] Different period detected (${subscription.plan_cycle} -> ${newPlanCycle}), fetching current subscription details`);

    // Get current subscription details from Razorpay to get remaining_count
    const currentSubscriptionDetails = await razorpayGetSubscription(subscription.razorpay_subscription_id);

    if (currentSubscriptionDetails.success && currentSubscriptionDetails.data) {
      const currentRemainingCount = currentSubscriptionDetails.data.remaining_count;

      // Calculate appropriate remaining_count for the target period
      let targetRemainingCount: number;

      if (subscription.plan_cycle === "monthly" && newPlanCycle === "yearly") {
        // Converting from monthly to yearly
        // Monthly plans have max 120 cycles, yearly plans have max 10 cycles
        // Convert remaining months to remaining years (rounded up to ensure coverage)
        targetRemainingCount = Math.min(Math.ceil(currentRemainingCount / 12), 10);
      } else if (subscription.plan_cycle === "yearly" && newPlanCycle === "monthly") {
        // Converting from yearly to monthly
        // Convert remaining years to remaining months
        targetRemainingCount = Math.min(currentRemainingCount * 12, 120);
      } else {
        // Same period (shouldn't happen due to isDifferentPeriod check, but safety fallback)
        targetRemainingCount = currentRemainingCount;
      }

      updateParams.remaining_count = targetRemainingCount;
      console.log(`[SUBSCRIPTION_CHANGE] Converting remaining_count from ${currentRemainingCount} (${subscription.plan_cycle}) to ${targetRemainingCount} (${newPlanCycle})`);
    } else {
      console.error(`[SUBSCRIPTION_CHANGE] Failed to fetch current subscription details:`, currentSubscriptionDetails.error);
      return createErrorResponse("Failed to fetch current subscription details for period change");
    }
  }

  // Update the subscription with the new plan
  const result = await razorpayUpdateSubscription(
    subscription.razorpay_subscription_id,
    updateParams
  );

  if (!result.success) {
    return createErrorResponse(
      typeof result.error === 'string'
        ? result.error
        : result.error
          ? JSON.stringify(result.error)
          : "Failed to update subscription plan"
    );
  }

  // Import edge-safe subscription logic to determine has_active_subscription
  const { EdgeSubscriptionStateManager } = await import('@/lib/subscription/edge-validation');
  const hasActiveSubscription = EdgeSubscriptionStateManager.shouldHaveActiveSubscription(
    subscription.subscription_status,
    newPlanId
  );

  // IMPORTANT: Don't update the database here - let the webhook handle it
  // When Razorpay updates the subscription, it will send a subscription.updated webhook
  // which will properly handle the database updates
  console.log(`[RAZORPAY_CHANGE] Successfully updated subscription ${subscription.razorpay_subscription_id} in Razorpay. Webhook will handle database updates.`);

  // Revalidate paths
  revalidateSubscriptionPaths();

  // Check if this is a plan downgrade
  const isPlanDowngrade =
    (subscription.plan_id === 'growth' && newPlanId === 'basic') ||
    (subscription.plan_id === 'growth' && newPlanId === 'free') ||
    (subscription.plan_id === 'basic' && newPlanId === 'free');

  // If this is a plan downgrade, log it
  if (isPlanDowngrade) {
    console.log(`[PLAN_CHANGE] Plan downgrade detected from ${subscription.plan_id} to ${newPlanId}`);
    console.log(`[PLAN_CHANGE] Product availability will be automatically handled by database trigger`);
  }

  return createSuccessResponse({
    message: "Your subscription plan has been updated successfully."
  });
}

/**
 * Change subscription plan
 * @param newPlanId The new plan ID
 * @param newPlanCycle The new plan cycle
 * @param forceNewSubscription Whether to force a new subscription
 * @returns The plan change result
 */
export async function changePlan(
  newPlanId: PlanId,
  newPlanCycle: PlanCycle,
  forceNewSubscription: boolean = false
): Promise<ActionResponse> {
  console.log("[CARD_PAYMENT_DEBUG] changePlan function called");

  // Get user and profile
  const { user, profile, error } = await getUserAndProfile(
    "has_active_subscription, trial_end_date"
  );

  if (error) {
    return createErrorResponse(error);
  }

  // Get subscription from payment_subscriptions table
  const supabase = await import("@/utils/supabase/admin").then(mod => mod.createAdminClient());
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("razorpay_subscription_id, subscription_status, plan_id, plan_cycle")
    .eq("business_profile_id", user?.id || "")
    .eq("subscription_status", "active")
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription:", subscriptionError);
    return createErrorResponse("Error fetching subscription details");
  }

  // Check if user is on trial
  const trialEndDate = profile?.trial_end_date
    ? new Date(profile.trial_end_date)
    : null;
  const isInTrial = trialEndDate && trialEndDate > new Date();

  // If user has a subscription ID, check its status
  if (subscription?.razorpay_subscription_id && isInTrial) {
    const subscriptionResult = await razorpayGetSubscription(subscription.razorpay_subscription_id);

    // If user is on trial and has an authenticated subscription, use switchAuthenticatedSubscription
    if (
      subscriptionResult.success &&
      subscriptionResult.data?.status === "authenticated"
    ) {
      console.log("[SUBSCRIPTION_DEBUG] User is on trial with authenticated subscription, using switchAuthenticatedSubscription");

      // Use our new function to handle switching from authenticated subscription to new plan
      const { switchAuthenticatedSubscription } = await import("./switch");
      return await switchAuthenticatedSubscription(
        subscription.razorpay_subscription_id,
        newPlanId,
        newPlanCycle
      );
    }
  }

  // If profile is null or user doesn't have an active subscription, return error
  if (!profile || !profile.has_active_subscription || !subscription?.razorpay_subscription_id) {
    return createErrorResponse("User does not have an active subscription");
  }

  // If no change in plan or cycle, return early
  if (
    subscription?.plan_id === newPlanId &&
    subscription?.plan_cycle === newPlanCycle
  ) {
    return createSuccessResponse({ no_change: true });
  }

  console.log(
    `[PAYMENT_METHOD] Handling plan change for subscription ${subscription.razorpay_subscription_id}`
  );

  // Get the Razorpay plan ID based on the plan type and cycle
  let razorpayPlanId: string;
  try {
    razorpayPlanId = getSubscriptionRazorpayPlanId(newPlanId, newPlanCycle);
  } catch (error) {
    return createErrorResponse(error instanceof Error ? error.message : "Invalid plan selected");
  }

  // Get current subscription details to check if we need to create a new subscription
  const subscriptionDetails = await razorpayGetSubscription(subscription.razorpay_subscription_id);

  if (!subscriptionDetails.success) {
    return createErrorResponse("Failed to get subscription details");
  }

  // Check if we need to force a new subscription based on payment method
  // For now, we'll assume all subscriptions can be updated
  const requiresNewSubscription = false;
  const currentEndDate = subscriptionDetails.data?.current_end
    ? new Date(subscriptionDetails.data.current_end * 1000).toISOString()
    : null;

  // If payment method doesn't support updates and we're not forcing a new subscription,
  // return special error with payment method information
  if (requiresNewSubscription && !forceNewSubscription) {
    return {
      success: false,
      error: "PAYMENT_METHOD_UPDATE_NOT_SUPPORTED",
      data: {
        payment_method: subscriptionDetails.data?.payment_method || "unknown",
        subscription_id: subscription.razorpay_subscription_id,
        current_plan: subscription.plan_id,
        current_cycle: subscription.plan_cycle,
        new_plan: newPlanId,
        new_cycle: newPlanCycle,
        current_end: currentEndDate,
      },
    };
  }

  // Check if we're changing to a different period (monthly <-> yearly)
  const isDifferentPeriod = subscription.plan_cycle !== newPlanCycle;
  const updateParams: {
    plan_id: string;
    schedule_change_at: string;
    customer_notify: boolean;
    remaining_count?: number;
  } = {
    plan_id: razorpayPlanId,
    schedule_change_at: "now",
    customer_notify: true
  };

  // If changing to a different period, we need to include remaining_count
  if (isDifferentPeriod) {
    console.log(`[SUBSCRIPTION_CHANGE] Different period detected (${subscription.plan_cycle} -> ${newPlanCycle}), using already fetched subscription details`);

    // We already have subscription details from earlier in this function
    if (subscriptionDetails.success && subscriptionDetails.data) {
      const currentRemainingCount = subscriptionDetails.data.remaining_count;

      // Calculate appropriate remaining_count for the target period
      let targetRemainingCount: number;

      if (subscription.plan_cycle === "monthly" && newPlanCycle === "yearly") {
        // Converting from monthly to yearly
        // Monthly plans have max 120 cycles, yearly plans have max 10 cycles
        // Convert remaining months to remaining years (rounded up to ensure coverage)
        targetRemainingCount = Math.min(Math.ceil(currentRemainingCount / 12), 10);
      } else if (subscription.plan_cycle === "yearly" && newPlanCycle === "monthly") {
        // Converting from yearly to monthly
        // Convert remaining years to remaining months
        targetRemainingCount = Math.min(currentRemainingCount * 12, 120);
      } else {
        // Same period (shouldn't happen due to isDifferentPeriod check, but safety fallback)
        targetRemainingCount = currentRemainingCount;
      }

      updateParams.remaining_count = targetRemainingCount;
      console.log(`[SUBSCRIPTION_CHANGE] Converting remaining_count from ${currentRemainingCount} (${subscription.plan_cycle}) to ${targetRemainingCount} (${newPlanCycle})`);
    } else {
      console.error(`[SUBSCRIPTION_CHANGE] Subscription details not available for period change`);
      return createErrorResponse("Failed to get subscription details for period change");
    }
  }

  // Update the subscription with the new plan
  const result = await razorpayUpdateSubscription(
    subscription.razorpay_subscription_id,
    updateParams
  );

  // Revalidate paths
  revalidateSubscriptionPaths();

  if (!result.success) {
    return createErrorResponse(
      typeof result.error === 'string'
        ? result.error
        : result.error
          ? JSON.stringify(result.error)
          : "Failed to update subscription"
    );
  }

  // Check if this is a plan downgrade
  const isPlanDowngrade =
    (subscription.plan_id === 'pro' && (newPlanId === 'growth' || newPlanId === 'basic' || newPlanId === 'free')) ||
    (subscription.plan_id === 'growth' && (newPlanId === 'basic' || newPlanId === 'free')) ||
    (subscription.plan_id === 'basic' && newPlanId === 'free');

  // If this is a plan downgrade, log it
  if (isPlanDowngrade) {
    console.log(`[PLAN_CHANGE] Plan downgrade detected from ${subscription.plan_id} to ${newPlanId}`);
    console.log(`[PLAN_CHANGE] Product availability will be automatically handled by database trigger`);
  }

  return createSuccessResponse({
    message: "Your subscription plan has been updated successfully."
  });
}
